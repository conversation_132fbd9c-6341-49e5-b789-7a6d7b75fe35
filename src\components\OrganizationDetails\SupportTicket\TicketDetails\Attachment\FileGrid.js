'use client';
import { Box, Tooltip, Typography, IconButton } from '@mui/material';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import CloseIcon from '@mui/icons-material/Close';

export default function FileGrid({
  displayFiles = [],
  onPreviewClick,
  onDownload,
  onRemove,
  showRemoveIcon = false,
}) {
  return (
    <Box className="file-grid-container pt24">
      {displayFiles?.map((media, index) => {
        return (
          <Box
            key={index}
            className="selected-files selected-view-files file-grid-item file-grid-item-with-hover"
          >
            {/* Cross Icon for Remove - Only show if onRemove is provided */}
            {onRemove && showRemoveIcon && (
              <IconButton
                className="file-remove-icon"
                onClick={() => onRemove(media, index)}
                size="small"
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            )}

            <Box className="file-name">
              <Box className="d-flex align-center gap-sm">
                <InsertDriveFileIcon className="file-icon" />
                <Typography className="title-text text-ellipsis-line text-capital">
                  {media?.name || 'Unknown File'}
                </Typography>
              </Box>
            </Box>
            <Box className="icons-wrap d-flex gap-sm">
              <Tooltip
                title={
                  <Typography className="sub-title-text">Preview</Typography>
                }
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
                arrow
              >
                <RemoveRedEyeOutlinedIcon
                  onClick={() => onPreviewClick(media)}
                  className="eye-icon"
                  sx={{ cursor: 'pointer' }}
                />
              </Tooltip>
              <Tooltip
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
                title={
                  <Typography className="sub-title-text">Download</Typography>
                }
                arrow
              >
                <FileDownloadOutlinedIcon
                  onClick={() =>
                    onDownload(
                      media?.downloadUrl || media?.preview,
                      media?.name
                    )
                  }
                  className="download-icon"
                  sx={{ cursor: 'pointer' }}
                />
              </Tooltip>
            </Box>
          </Box>
        );
      })}
    </Box>
  );
}
