.report-main-container {
  background-color: var(--color-white);
}
.report-table-container {
  margin-top: var(--spacing-lg);
}

.report-header-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  border-bottom: var(--normal-sec-border);

  .report-header-tabs-wrap {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;

    .MuiTabs-root {
      border-bottom: none;
    }
  }
  @media (max-width: 899px) {
    overflow-x: auto;
    ::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}
