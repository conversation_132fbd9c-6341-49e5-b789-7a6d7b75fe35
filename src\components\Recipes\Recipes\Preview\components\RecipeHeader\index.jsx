import React, { useContext, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Tooltip, Typography } from '@mui/material';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import SplideThumbnailCarousel from '@/components/SplideCarousel/SplideThumbnailCarousel';
import MediaIconsCarousel from '@/components/UI/MediaIconsCarousel';
import AudioPlayer from '@/components/UI/AudioPlayer';
import NutritionCard from '../NutritionCard';
import {
  getCurrencySymbol,
  getFormattedTotalTime,
  IngredientIconSize,
} from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import RecipesIcon from '@/components/UI/RecipePlaceholderIcon/RecipesIcon';
import NoDataView from '@/components/UI/NoDataView';
import AppImage from '@/components/UI/AppImage/AppImage';
import { RecipePlaceholder } from '@/helper/common/recipesImgPlaceholder';
import CommonNoDataImage from '../../../CommonNoDataImage';
import NewRemovedIndicator from './NewRemovedIndicator';
import './RecipeHeader.scss';

const RecipeHeader = ({
  recipeData,
  isPublicPage = false,
  highlightData = null,
  isHighlightingActive = false,
  addedFields = null,
  removedFields = null,
}) => {
  const router = useRouter();
  const { org_name } = useParams();
  const [selectedAudio, setSelectedAudio] = useState(null);
  const { authState } = useContext(AuthContext);
  const currency = getCurrencySymbol(authState?.currency_details);

  const handleAudioClick = (audioItem) => {
    setSelectedAudio(audioItem);
  };

  const handleCloseAudio = () => {
    setSelectedAudio(null);
  };

  const handleBack = () => {
    // Determine proper back navigation based on current route
    if (isPublicPage) {
      // For public pages, go to public recipes list or home
      router.push(`/recipe/${org_name}`);
    } else {
      // For private pages, go to recipes list
      router.push('/recipes');
    }
  };

  // Helper function to get appropriate icon based on resource type
  const getIconForResourceType = (itemType) => {
    const type = itemType?.toLowerCase() || '';

    if (
      type.includes('pdf') ||
      type.includes('document') ||
      type.includes('wordprocessing') ||
      type.includes('msword')
    ) {
      return 'FileText';
    } else if (
      type.includes('audio') ||
      type.includes('mpeg') ||
      type.includes('mp3') ||
      type.includes('wav')
    ) {
      return 'Music';
    } else if (type.includes('youtube')) {
      return 'Youtube';
    } else if (type.includes('video')) {
      return 'Play';
    } else if (type.includes('image')) {
      return 'Image';
    } else {
      return 'Link';
    }
  };

  // Use actual API data only
  const resourcesData = recipeData?.resources || [];

  // Helper function to check if resource is image/video for SplideThumbnailCarousel
  const isImageOrVideo = (resource) => {
    const itemType = resource?.item_detail?.item_type?.toLowerCase() || '';
    const itemLink = resource?.item_detail?.item_link || '';

    // List of viewable image/video extensions
    const viewableExtensions = [
      '.jpg',
      '.jpeg',
      '.png',
      '.gif',
      '.bmp',
      '.webp',
      '.svg', // images
      '.mp4',
      '.webm',
      '.ogg',
      '.mov',
      '.avi',
      '.mkv', // videos
    ];

    // Check extension
    const hasViewableExtension = viewableExtensions.some((ext) =>
      itemLink.toLowerCase().endsWith(ext)
    );

    // Check MIME type
    const isViewableType =
      itemType === 'image' || itemType === 'video' || itemType === 'youtube';

    // Only include if both type and extension are viewable
    return isViewableType && hasViewableExtension;
  };

  // Helper function to check if resource is PDF/Audio/Link for MediaIconsCarousel
  const isPdfAudioOrLink = (resource) => {
    const itemType = resource?.item_detail?.item_type?.toLowerCase();
    const resourceType = resource?.type?.toLowerCase();

    // Check item_type specifically for PDF, Audio, and generic links (not youtube/video)
    const isPdf =
      itemType?.includes('pdf') ||
      itemType?.includes('application/pdf') ||
      itemType?.includes('document') ||
      itemType?.includes('vnd.openxmlformats') ||
      itemType?.includes('msword') ||
      itemType?.includes('wordprocessing');

    const isAudio =
      itemType?.includes('audio') ||
      itemType?.includes('mpeg') ||
      itemType?.includes('mp3') ||
      itemType?.includes('wav') ||
      itemType?.includes('ogg') ||
      itemType?.includes('m4a') ||
      itemType?.includes('aac') ||
      resourceType === 'audio';

    const isGenericLink =
      resourceType === 'link' &&
      !itemType?.includes('youtube') &&
      !itemType?.includes('video') &&
      !itemType?.includes('image');

    return isPdf || isAudio || isGenericLink;
  };

  // Filter resources for SplideThumbnailCarousel (Images & Videos only)
  const imageVideoResources = resourcesData?.filter(isImageOrVideo) || [];

  // Filter resources for MediaIconsCarousel (PDF, Audio, Links only)
  const pdfAudioLinkResources = resourcesData?.filter(isPdfAudioOrLink) || [];

  // Transform filtered resources into media data for SplideThumbnailCarousel
  const resourceMediaData =
    imageVideoResources?.map((resource) => resource?.item_detail?.item_link) ||
    [];

  // Create mediaData array with main recipe image as placeholder (first item) if available
  const mediaData = [];

  // Add main recipe image as placeholder if it exists
  if (recipeData?.item_detail?.item_link) {
    mediaData.push(recipeData?.item_detail?.item_link);
  }

  // Add additional images/videos from resources
  mediaData.push(...resourceMediaData);

  // Transform filtered resources into media icons for MediaIconsCarousel
  const mediaIcons =
    pdfAudioLinkResources?.map((resource) => {
      const icon = getIconForResourceType(resource?.item_detail?.item_type);
      const itemType = resource?.item_detail?.item_type?.toLowerCase() || '';

      // Map the type correctly for MediaIconsCarousel component
      let mappedType = resource?.type;
      if (
        itemType?.includes('pdf') ||
        itemType?.includes('document') ||
        itemType?.includes('wordprocessing') ||
        itemType?.includes('msword')
      ) {
        mappedType = 'document'; // MediaIconsCarousel expects 'document' for PDFs
      } else if (
        itemType?.includes('audio') ||
        itemType?.includes('mpeg') ||
        itemType?.includes('mp3') ||
        itemType?.includes('wav') ||
        itemType?.includes('ogg') ||
        itemType?.includes('m4a') ||
        itemType?.includes('aac') ||
        resource?.type?.toLowerCase() === 'audio'
      ) {
        mappedType = 'audio';
      } else {
        mappedType = 'link'; // For other link types
      }

      return {
        id: resource?.id,
        type: mappedType,
        icon: icon,
        url: resource?.item_detail?.item_link,
      };
    }) || [];

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'low':
      case 'easy':
        return 'recipe-header__difficulty--low';
      case 'medium':
        return 'recipe-header__difficulty--medium';
      case 'hard':
      case 'difficult':
        return 'recipe-header__difficulty--hard';
      default:
        return 'recipe-header__difficulty--default';
    }
  };

  // Helper function to render title with highlighting
  const renderTitleWithHighlight = () => {
    if (isHighlightingActive && highlightData?.recipe_title) {
      const oldTitle = highlightData.recipe_title;

      if (
        oldTitle !== undefined &&
        oldTitle !== null &&
        oldTitle !== recipeData?.recipe_title
      ) {
        return (
          <div className="highlight-container">
            <p className="recipe-header__title highlight-no-margin-bottom">
              {recipeData?.recipe_title}
            </p>
            <p className="highlight-original-text">{oldTitle}</p>
          </div>
        );
      }
    }

    // Normal display
    return <p className="recipe-header__title">{recipeData?.recipe_title}</p>;
  };

  // Helper function to render description with highlighting
  const renderDescriptionWithHighlight = () => {
    if (isHighlightingActive && highlightData?.recipe_description) {
      const oldDescription = highlightData.recipe_description;

      if (
        oldDescription !== undefined &&
        oldDescription !== null &&
        oldDescription !== recipeData?.recipe_description
      ) {
        return (
          <div className="highlight-container">
            <p className="recipe-header__subtitle highlight-no-margin-bottom">
              {recipeData?.recipe_description}
            </p>
            <p className="highlight-original-text">{oldDescription}</p>
          </div>
        );
      }
    }

    // Normal display
    return (
      <p className="recipe-header__subtitle">
        {recipeData?.recipe_description}
      </p>
    );
  };

  // Helper function to render cook time with highlighting
  const renderCookTimeWithHighlight = () => {
    if (
      isHighlightingActive &&
      (highlightData?.recipe_cook_time ||
        highlightData?.recipe_preparation_time)
    ) {
      const oldCookTime = Math.round(
        (highlightData.recipe_cook_time || recipeData?.recipe_cook_time) / 60 +
          (highlightData.recipe_preparation_time ||
            recipeData?.recipe_preparation_time) /
            60
      );
      const currentCookTime = Math.round(
        (recipeData?.recipe_cook_time / 60 || 0) +
          (recipeData?.recipe_preparation_time / 60 || 0)
      );

      if (
        oldCookTime !== undefined &&
        oldCookTime !== null &&
        oldCookTime !== currentCookTime
      ) {
        return (
          <div className="highlight-container">
            <p className="recipe-header__stat-value highlight-no-margin-bottom">
              {getFormattedTotalTime(currentCookTime)}
            </p>
            <p className="highlight-original-text">
              {getFormattedTotalTime(oldCookTime)}
            </p>
          </div>
        );
      }
    }

    // Normal display
    return (
      <p className="recipe-header__stat-value">
        {getFormattedTotalTime(
          recipeData?.recipe_cook_time / 60 +
            recipeData?.recipe_preparation_time / 60
        )}
      </p>
    );
  };

  // Helper function to render yield with highlighting
  const renderYieldWithHighlight = () => {
    if (isHighlightingActive && highlightData?.recipe_yield) {
      const oldYield = highlightData.recipe_yield;

      if (
        oldYield !== undefined &&
        oldYield !== null &&
        oldYield !== recipeData?.recipe_yield
      ) {
        return (
          <div className="highlight-container">
            <p className="recipe-header__stat-value highlight-no-margin-bottom">
              {recipeData?.recipe_yield}
            </p>
            <p className="highlight-original-text">{oldYield}</p>
          </div>
        );
      }
    }

    // Normal display
    return (
      <p className="recipe-header__stat-value">{recipeData?.recipe_yield}</p>
    );
  };

  // Helper function to render total portions with highlighting
  const renderTotalPortionsWithHighlight = () => {
    if (isHighlightingActive && highlightData?.recipe_total_portions) {
      const oldTotalPortions = highlightData.recipe_total_portions;

      if (
        oldTotalPortions !== undefined &&
        oldTotalPortions !== null &&
        oldTotalPortions !== recipeData?.recipe_total_portions
      ) {
        return (
          <div className="highlight-container">
            <p className="recipe-header__stat-value highlight-no-margin-bottom">
              {currency}
              {recipeData?.recipe_total_portions}
            </p>
            <p className="highlight-original-text">
              {currency}
              {oldTotalPortions}
            </p>
          </div>
        );
      }
    }

    // Normal display
    return (
      <p className="recipe-header__stat-value">
        {currency}
        {recipeData?.recipe_total_portions}
      </p>
    );
  };

  // Helper function to render single portion size with highlighting
  const renderSinglePortionSizeWithHighlight = () => {
    if (isHighlightingActive && highlightData?.recipe_single_portion_size) {
      const oldSinglePortionSize = highlightData.recipe_single_portion_size;

      if (
        oldSinglePortionSize !== undefined &&
        oldSinglePortionSize !== null &&
        oldSinglePortionSize !== recipeData?.recipe_single_portion_size
      ) {
        return (
          <div className="highlight-container">
            <p className="recipe-header__stat-value highlight-no-margin-bottom">
              {recipeData?.recipe_single_portion_size.toFixed(2)}g
            </p>
            <p className="highlight-original-text">
              {oldSinglePortionSize.toFixed(2)}g
            </p>
          </div>
        );
      }
    }

    // Normal display
    return (
      <p className="recipe-header__stat-value">
        {recipeData?.recipe_single_portion_size.toFixed(2)}g
      </p>
    );
  };

  // Helper function to render complexity level with highlighting
  const renderComplexityLevelWithHighlight = () => {
    if (isHighlightingActive && highlightData?.recipe_complexity_level) {
      const oldComplexityLevel = highlightData.recipe_complexity_level;

      if (
        oldComplexityLevel !== undefined &&
        oldComplexityLevel !== null &&
        oldComplexityLevel !== recipeData?.recipe_complexity_level
      ) {
        return (
          <div className="highlight-container">
            <p className="recipe-header__difficulty-title highlight-no-margin-bottom">
              {recipeData?.recipe_complexity_level}
            </p>
            <p className="highlight-original-text"> {oldComplexityLevel}</p>
          </div>
        );
      }
    }

    // Normal display
    return (
      <p className="recipe-header__difficulty-title">
        {recipeData?.recipe_complexity_level}
      </p>
    );
  };

  // Helper function to render allergen warning with highlighting and new/removed indicators
  const renderAllergenWarningWithHighlight = () => {
    if (isHighlightingActive && highlightData?.allergen_attributes) {
      const oldAllergenData = highlightData.allergen_attributes;
      const currentAllergenData = recipeData?.allergen_attributes;
      // Check if allergen data has changed
      const hasChanged = oldAllergenData;
      if (hasChanged) {
        return (
          <div className="highlight-container">
            <div className="recipe-header__allergen-warning highlight-no-margin-bottom">
              <Icon
                name="AlertTriangle"
                size={20}
                className="recipe-header__allergen-icon"
              />
              <div>
                <p className="recipe-header__allergen-title">
                  Allergen Warning
                </p>
                {/* Current allergen content */}
                {currentAllergenData?.contains &&
                currentAllergenData?.contains.length > 0 ? (
                  <div className="recipe-header__allergen-icons-wrap flex-wrap">
                    {currentAllergenData?.contains?.map((allergen, index) => (
                      <Tooltip
                        key={index}
                        title={
                          <Typography>
                            {allergen?.attribute_title} Contain
                          </Typography>
                        }
                        classes={{ tooltip: 'info-tooltip-container ' }}
                        arrow
                      >
                        <span className="recipe-header__allergen-icon-wrapper">
                          <RecipesIcon
                            iconUrl={allergen?.item_detail?.item_link}
                            altText={allergen?.attribute_title}
                            imgWidth={IngredientIconSize}
                            imgHeight={IngredientIconSize}
                            className="recipe-header__allergen-icon-item"
                          />
                          {isItemNew(allergen.id, 'allergen_attributes') && (
                            <NewRemovedIndicator type="new" />
                          )}
                        </span>
                      </Tooltip>
                    ))}
                    {currentAllergenData?.may_contain?.map(
                      (allergen, index) => (
                        <div
                          key={index}
                          className="recipe-header__may-allergen-icon"
                        >
                          <RecipesIcon
                            iconUrl={allergen?.item_detail?.item_link}
                            altText={allergen?.attribute_title}
                            imgWidth={IngredientIconSize}
                            imgHeight={IngredientIconSize}
                            className="recipe-header__allergen-icon-item"
                          />
                          <Tooltip
                            title={
                              <Typography>
                                {allergen?.attribute_title} May Contain
                              </Typography>
                            }
                            classes={{ tooltip: 'info-tooltip-container ' }}
                            arrow
                          >
                            <span className="recipe-header__may-allergen-indicator">
                              M
                            </span>
                          </Tooltip>
                          {isItemNew(allergen.id, 'allergen_attributes') && (
                            <NewRemovedIndicator type="new" />
                          )}
                        </div>
                      )
                    )}
                  </div>
                ) : (
                  <p className="recipe-header__allergen-text">
                    No allergens identified
                  </p>
                )}
              </div>
            </div>
            {/* Original allergen content */}
            <div className="highlight-original-allergen">
              <div>
                {oldAllergenData && oldAllergenData?.length > 0 ? (
                  <div className="recipe-header__allergen-icons-wrap old-allergen-icons-wrap flex-wrap">
                    {oldAllergenData?.map((allergen, index) => {
                      return (
                        <Tooltip
                          key={index}
                          title={
                            <Typography>
                              {allergen?.attribute_title} Contain
                            </Typography>
                          }
                          classes={{ tooltip: 'info-tooltip-container ' }}
                          arrow
                        >
                          <span className="highlight-original-allergen-icon-wrapper">
                            <RecipesIcon
                              iconUrl={allergen?.item_detail?.item_link}
                              altText={allergen?.attribute_title}
                              imgWidth={IngredientIconSize}
                              imgHeight={IngredientIconSize}
                              className="recipe-header__allergen-icon-item"
                            />
                            {isItemRemoved(
                              allergen.id,
                              'allergen_attributes'
                            ) && <NewRemovedIndicator type="removed" />}
                          </span>
                        </Tooltip>
                      );
                    })}
                  </div>
                ) : (
                  <p className="recipe-header__allergen-text">
                    No allergens identified
                  </p>
                )}
              </div>
            </div>
          </div>
        );
      }
    }

    // Normal display
    return (
      <div className="recipe-header__allergen-warning">
        <Icon
          name="AlertTriangle"
          size={20}
          className="recipe-header__allergen-icon"
        />
        <div>
          <p className="recipe-header__allergen-title">Allergen Warning</p>
          {recipeData?.allergen_attributes?.contains &&
          recipeData?.allergen_attributes?.contains.length > 0 ? (
            <div className="recipe-header__allergen-icons-wrap flex-wrap">
              {recipeData?.allergen_attributes?.contains?.map(
                (allergen, index) => (
                  <Tooltip
                    key={index}
                    title={
                      <Typography>
                        {allergen?.attribute_title} Contain
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container ',
                    }}
                    arrow
                  >
                    <span className="recipe-header__allergen-icon-wrapper">
                      <RecipesIcon
                        iconUrl={allergen?.item_detail?.item_link || ''}
                        altText={allergen?.attribute_title}
                        imgWidth={IngredientIconSize}
                        imgHeight={IngredientIconSize}
                        className="recipe-header__allergen-icon-item"
                      />
                      {isItemNew(allergen.id, 'allergen_attributes') && (
                        <NewRemovedIndicator type="new" />
                      )}
                    </span>
                  </Tooltip>
                )
              )}
              {recipeData?.allergen_attributes?.may_contain?.map(
                (allergen, index) => (
                  <div key={index} className="recipe-header__may-allergen-icon">
                    <RecipesIcon
                      iconUrl={allergen?.item_detail?.item_link || ''}
                      altText={allergen?.attribute_title}
                      imgWidth={IngredientIconSize}
                      imgHeight={IngredientIconSize}
                      className="recipe-header__allergen-icon-item"
                    />
                    <Tooltip
                      title={
                        <Typography>
                          {allergen?.attribute_title} May Contain
                        </Typography>
                      }
                      classes={{
                        tooltip: 'info-tooltip-container ',
                      }}
                      arrow
                    >
                      <span className="recipe-header__may-allergen-indicator">
                        M
                      </span>
                    </Tooltip>
                    {isItemNew(allergen.id, 'allergen_attributes') && (
                      <NewRemovedIndicator type="new" />
                    )}
                  </div>
                )
              )}
            </div>
          ) : (
            <p className="recipe-header__allergen-text">
              No allergens identified
            </p>
          )}
        </div>
      </div>
    );
  };

  // Helper function to render dietary with highlighting and new/removed indicators
  const renderDietaryWithHighlight = () => {
    if (isHighlightingActive && highlightData?.dietary_attributes) {
      const oldDietry = highlightData.dietary_attributes;
      const currentDietry = recipeData?.dietary_attributes;

      // Check if dietary have changed
      const hasChanged =
        JSON.stringify(oldDietry) !== JSON.stringify(currentDietry);

      if (hasChanged) {
        return (
          <div className="highlight-container">
            <p className="recipe-header__allergen-title">Dietary Suitability</p>
            <div className="recipe-header__categories highlight-no-margin-bottom">
              {currentDietry && currentDietry.length > 0 ? (
                currentDietry.map((allergen, index) => (
                  <span key={index} className="recipe-header__dietary-tag">
                    {allergen?.attribute_title}
                    {isItemNew(allergen.id, 'dietary_attributes') && (
                      <NewRemovedIndicator type="new" />
                    )}
                  </span>
                ))
              ) : (
                <span className="recipe-header__dietary-tag recipe-header__dietary-tag--empty">
                  No categories assigned
                </span>
              )}
            </div>
            <div className="highlight-original-categories d-flex align-center gap-5 mt8 flex-wrap">
              {oldDietry && oldDietry.length > 0 ? (
                oldDietry?.map((allergen, index) => (
                  <span
                    key={index}
                    className="recipe-header__dietary-tag recipe-header__dietary-tag--highlight highlight-original-text"
                  >
                    {allergen?.attribute_title}
                    {isItemRemoved(allergen.id, 'dietary_attributes') && (
                      <NewRemovedIndicator type="removed" />
                    )}
                  </span>
                ))
              ) : (
                <span className="recipe-header__dietary-tag recipe-header__dietary-tag--empty recipe-header__dietary-tag--highlight">
                  No categories assigned
                </span>
              )}
            </div>
          </div>
        );
      }
    }

    // Normal display
    return (
      <div>
        <p className="recipe-header__allergen-title">Dietary Suitability</p>
        <div className="recipe-header__categories">
          {recipeData?.dietary_attributes &&
          recipeData?.dietary_attributes.length > 0 ? (
            recipeData?.dietary_attributes?.map((allergen, index) => (
              <span key={index} className="recipe-header__dietary-tag">
                {allergen?.attribute_title}
                {isItemNew(allergen.id, 'dietary_attributes') && (
                  <NewRemovedIndicator type="new" />
                )}
              </span>
            ))
          ) : (
            <span className="recipe-header__dietary-tag recipe-header__dietary-tag--empty">
              No categories assigned
            </span>
          )}
        </div>
      </div>
    );
  };

  // Helper function to render nutrition card with highlighting
  const renderNutritionCardWithHighlight = () => {
    if (isHighlightingActive && highlightData?.nutrition_attributes) {
      const oldNutritionData = highlightData.nutrition_attributes;
      const currentNutritionData = recipeData?.nutrition_attributes;

      // Check if nutrition data has actually changed by comparing values
      const hasChanged = oldNutritionData?.some((oldItem) => {
        const currentItem = currentNutritionData?.find(
          (item) => item?.attribute_slug === oldItem?.attribute_slug
        );

        // Compare values if both items exist
        if (currentItem && oldItem?.value !== undefined) {
          const oldValue = parseFloat(oldItem?.value) || 0;
          const currentValue = parseFloat(currentItem?.value) || 0;
          return oldValue !== currentValue;
        }

        return false;
      });

      if (hasChanged) {
        return (
          <NutritionCard
            recipeData={recipeData}
            isHighlighted={true}
            originalNutritionData={oldNutritionData}
            addedFields={addedFields}
            removedFields={removedFields}
          />
        );
      }
    }

    // Normal display
    return (
      <NutritionCard
        recipeData={recipeData}
        addedFields={addedFields}
        removedFields={removedFields}
      />
    );
  };

  // Helper function to render categories with highlighting and new/removed indicators
  const renderCategoriesWithHighlight = () => {
    if (isHighlightingActive && highlightData?.categories) {
      const oldCategories = highlightData.categories;
      const currentCategories = recipeData?.categories;

      // Check if categories have changed
      const hasChanged =
        JSON.stringify(oldCategories) !== JSON.stringify(currentCategories);

      if (hasChanged) {
        return (
          <div className="highlight-container">
            <div className="recipe-header__categories highlight-no-margin-bottom">
              {currentCategories && currentCategories.length > 0 ? (
                currentCategories.map((category, index) => (
                  <span key={index} className="recipe-header__category-tag">
                    {category?.category_name}
                    {isItemNew(category.id, 'categories') && (
                      <NewRemovedIndicator type="new" />
                    )}
                  </span>
                ))
              ) : (
                <span className="recipe-header__category-tag recipe-header__category-tag--empty">
                  No categories assigned
                </span>
              )}
            </div>
            <div className="highlight-original-categories d-flex align-center gap-5 mt8 flex-wrap">
              {oldCategories && oldCategories.length > 0 ? (
                oldCategories?.map((category, index) => (
                  <span
                    key={index}
                    className="recipe-header__category-tag recipe-header__category-tag--highlight highlight-original-text"
                  >
                    {category?.category_name}
                    {isItemRemoved(category.id, 'categories') && (
                      <NewRemovedIndicator type="removed" />
                    )}
                  </span>
                ))
              ) : (
                <span className="recipe-header__category-tag recipe-header__category-tag--empty recipe-header__category-tag--highlight">
                  No categories assigned
                </span>
              )}
            </div>
          </div>
        );
      }
    }

    // Normal display
    return (
      <div className="recipe-header__categories">
        {recipeData.categories && recipeData.categories.length > 0 ? (
          recipeData.categories.map((category, index) => (
            <span key={index} className="recipe-header__category-tag">
              {category?.category_name}
              {isItemNew(category.id, 'categories') && (
                <NewRemovedIndicator type="new" />
              )}
            </span>
          ))
        ) : (
          <span className="recipe-header__category-tag recipe-header__category-tag--empty">
            No categories assigned
          </span>
        )}
      </div>
    );
  };

  // Helper functions to check if items are new or removed
  const isItemNew = (itemId, fieldType) => {
    if (!addedFields || !addedFields[fieldType]) return false;
    return addedFields[fieldType].some((item) => item.id === itemId);
  };

  const isItemRemoved = (itemId, fieldType) => {
    if (!removedFields || !removedFields[fieldType]) return false;
    return removedFields[fieldType].some((item) => item.id === itemId);
  };

  const hasStatsData =
    recipeData?.recipe_cook_time > 0 ||
    recipeData?.recipe_preparation_time > 0 ||
    recipeData?.recipe_yield > 0 ||
    recipeData?.recipe_total_portions > 0 ||
    recipeData?.recipe_single_portion_size > 0;

  return (
    <div className="recipe-header">
      {/* Back button for public pages only */}
      {isPublicPage && (
        <div className="recipe-header__back-section">
          <CustomButton
            variant="outlined"
            title="Back"
            // startIcon={<Icon name="ArrowLeft" size={16} />}
            onClick={handleBack}
            className="recipe-header__back-btn"
          />
        </div>
      )}
      <div className="recipe-header__content">
        <div className="recipe-header__info">
          {/* Show media: always for private pages, data-driven for public pages */}
          <div className="hide-on-print">
            {(!isPublicPage ||
              (isPublicPage && mediaData && mediaData.length > 0)) && (
              <>
                {mediaData?.length > 0 ? (
                  <SplideThumbnailCarousel
                    media={mediaData}
                    thumbPosition="right"
                  />
                ) : (
                  <NoDataView
                    image={<CommonNoDataImage />}
                    title="No Media Available"
                    description="There are no cooking media available for this recipe at the moment."
                    className="no-data-auto-height-conainer"
                  />
                )}
              </>
            )}
          </div>
          <div className="view-on-print">
            <AppImage
              src={recipeData?.item_detail?.item_link}
              alt={'Recipe Placeholder'}
              className="recipe-header__recipe-placeholder"
              isSvg={true}
              SvgImg={RecipePlaceholder}
            />
          </div>
          {/* Audio Player - Show when any audio is selected from media icons */}
          {selectedAudio && (
            <AudioPlayer
              audioUrl={selectedAudio?.url}
              onClose={handleCloseAudio}
            />
          )}

          {/* Show links (media icons): always for private pages, data-driven for public pages */}
          {(!isPublicPage ||
            (isPublicPage && mediaIcons && mediaIcons.length > 0)) && (
            <MediaIconsCarousel
              mediaIcons={mediaIcons}
              onAudioClick={handleAudioClick}
            />
          )}

          <div className="recipe-header__info-content">
            {/* Show nutritional information: always for private pages, data-driven for public pages */}
            {(!isPublicPage ||
              (isPublicPage &&
                recipeData?.nutrition_attributes &&
                recipeData?.nutrition_attributes?.length > 0)) &&
              renderNutritionCardWithHighlight()}
          </div>
        </div>

        <div className="recipe-header__stats">
          <div className="recipe-header__title-info">
            {renderTitleWithHighlight()}
            {renderDescriptionWithHighlight()}
          </div>
          {hasStatsData && (
            <div className="recipe-header__stats-grid">
              {/* Show total time: always for private pages, data-driven for public pages */}
              {(!isPublicPage ||
                (isPublicPage &&
                  (recipeData?.recipe_cook_time > 0 ||
                    recipeData?.recipe_preparation_time > 0))) &&
                (recipeData?.recipe_cook_time > 0 ||
                  recipeData?.recipe_preparation_time > 0) && (
                  <div className="recipe-header__stat-card recipe-header__stat-card--time">
                    <Icon
                      name="Clock"
                      size={16}
                      color="var(--color-warning)"
                      className="recipe-header__stat-icon"
                    />
                    {renderCookTimeWithHighlight()}
                    <p className="recipe-header__stat-label">Total Time</p>
                  </div>
                )}

              {/* Show yield/portioning: always for private pages, data-driven for public pages */}
              {(!isPublicPage ||
                (isPublicPage && recipeData?.recipe_yield > 0)) &&
                recipeData?.recipe_yield > 0 && (
                  <div className="recipe-header__stat-card recipe-header__stat-card--portions">
                    <Icon
                      name="Users"
                      size={16}
                      color="var(--color-success)"
                      className="recipe-header__stat-icon"
                    />
                    {renderYieldWithHighlight()}
                    <p className="recipe-header__stat-label">Portions</p>
                  </div>
                )}

              {/* Show total portions: always for private pages, data-driven for public pages */}
              {(!isPublicPage ||
                (isPublicPage && recipeData?.recipe_total_portions > 0)) &&
                recipeData?.recipe_total_portions > 0 && (
                  <div className="recipe-header__stat-card recipe-header__stat-card--cost">
                    <Icon
                      name={currency}
                      size={16}
                      color="var(--color-danger)"
                      className="recipe-header__stat-icon"
                    />
                    {renderTotalPortionsWithHighlight()}
                    <p className="recipe-header__stat-label">Per Portion</p>
                  </div>
                )}

              {/* Show scale/portion size: always for private pages, data-driven for public pages */}
              {(!isPublicPage ||
                (isPublicPage && recipeData?.recipe_single_portion_size > 0)) &&
                recipeData?.recipe_single_portion_size > 0 && (
                  <div className="recipe-header__stat-card recipe-header__stat-card--size">
                    <Icon
                      name="Scale"
                      size={20}
                      color="var(--color-warning)"
                      className="recipe-header__stat-icon"
                    />
                    {renderSinglePortionSizeWithHighlight()}
                    <p className="recipe-header__stat-label">Portion Size</p>
                  </div>
                )}
            </div>
          )}
          {/* Show categories: always for private pages, data-driven for public pages */}
          {(!isPublicPage ||
            (isPublicPage &&
              recipeData?.categories &&
              recipeData?.categories?.length > 0)) &&
            renderCategoriesWithHighlight()}
          {recipeData?.recipe_complexity_level && (
            <div
              className={`recipe-header__difficulty ${getDifficultyColor(recipeData?.recipe_complexity_level)}`}
            >
              <Icon name="ChefHat" size={18} />
              {renderComplexityLevelWithHighlight()}
            </div>
          )}
          {/* Show allergen information: always show when allergens exist */}
          {recipeData?.allergen_attributes?.contains &&
            recipeData?.allergen_attributes?.contains.length > 0 &&
            renderAllergenWarningWithHighlight()}
          {/* Show dietary information: always for private pages, data-driven for public pages */}
          {(!isPublicPage ||
            (isPublicPage &&
              recipeData?.dietary_attributes &&
              recipeData?.dietary_attributes.length > 0)) &&
            renderDietaryWithHighlight()}
        </div>
      </div>
    </div>
  );
};

export default RecipeHeader;
