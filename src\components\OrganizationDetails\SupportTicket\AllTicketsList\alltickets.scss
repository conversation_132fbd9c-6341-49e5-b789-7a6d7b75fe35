.all-tickets-container-wrap {
  .all-tickets-container {
    // Simple fix: Add max-height for scroll functionality
    .all-tickets-header {
      margin-bottom: var(--spacing-xl);
    }

    .tickets-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: var(--spacing-lg);

      // @media (max-width: 1199px) {
      //   grid-template-columns: repeat(3, 1fr);
      //   gap: var(--spacing-md);
      // }

      // @media (max-width: 899px) {
      //   grid-template-columns: 1fr;
      //   gap: var(--spacing-md);
      // }

      .ticket-card-wrapper {
        // width: 100%;

        // Ensure ticket cards take full width of grid cell
        .all-tickets-list-container {
          // width: 100%;

          .ticket-wrap {
            width: 100%;
            margin-bottom: 0;
            transition: all 0.2s ease;
            cursor: pointer;

            &:hover:not(.selected) {
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              transform: translateY(-0.5px);
            }

            &.selected {
              box-shadow: 0 4px 12px rgba(19, 94, 150, 0.2);
              transform: translateY(-1px);
              transition: all 0.2s ease;
            }

            // Override name-time layout for All Tickets page
            .name-time-wrap {
              .name-wrap {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
                padding-bottom: 0;
              }

              .time-wrap {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
              }
            }
            @media (max-width: 372px) {
              max-width: 260px;
            }
          }
        }
        @media (max-width: 372px) {
          max-width: 260px;
        }
      }
    }
  }
  .all-ticket-pagination-wrap {
    @media (max-width: 1549px) {
      margin-bottom: var(--spacing-7xl);
    }
    @media (max-width: 1499px) {
      margin-bottom: var(--spacing-none);
    }
    @media (max-width: 490px) {
      margin-bottom: var(--spacing-7xl);
    }
  }
}

.all-ticket-header-wrap {
  .all-ticket-filter-wrap {
    @media (max-width: 1769px) {
      margin: var(--spacing-md) 0;
    }
    @media (max-width: 1549px) {
      width: 100%;
      justify-content: flex-end;
      flex-wrap: wrap;
      margin: var(--spacing-md) 0;
    }
    @media (max-width: 490px) {
      margin-bottom: var(--spacing-md);
    }
  }
  @media (max-width: 490px) {
    flex-wrap: wrap;
  }
}

// Responsive adjustments for ticket cards in grid
@media (max-width: 1499px) {
  .all-tickets-container .tickets-grid .ticket-card-wrapper {
    .all-tickets-list-container .ticket-wrap {
      padding: var(--spacing-md);

      .heading-text-wrap {
        font-size: var(--font-size-sm);
      }

      .description-text {
        font-size: var(--font-size-xs);
      }
    }
  }
}

@media (max-width: 899px) {
  .all-tickets-container .tickets-grid .ticket-card-wrapper {
    .all-tickets-list-container .ticket-wrap {
      padding: var(--spacing-sm);

      .name-time-wrap {
        .user-icon,
        .time-icon {
          font-size: var(--font-size-sm);
        }

        .name-text,
        .time-text {
          font-size: var(--font-size-xs);
        }
      }
    }
  }
}
