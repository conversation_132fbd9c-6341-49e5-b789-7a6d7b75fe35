.ingredients-card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  border: var(--border-width-xs) solid var(--color-light-gray);
  box-shadow: var(--box-shadow-xs);

  // Note: Highlighting styles are now global classes in globals.scss

  &__header {
    padding: var(--spacing-lg) var(--spacing-xxl);
    border-bottom: var(--border-width-xs) solid var(--color-light-gray);
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-xs);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__content {
    padding: var(--spacing-lg);
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__item {
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    position: relative;
    // Disabled/removed ingredient styling
    &--disabled {
      opacity: 0.6;
      background-color: var(--color-light-grayish-blue);
      border-color: var(--color-light-gray);

      // Add strikethrough effect to ingredient name
      .ingredients-card__item-name {
        text-decoration: line-through;
        color: var(--text-color-slate-gray);
      }

      // Muted colors for all text content
      .ingredients-card__item-cost,
      .ingredients-card__detail-value,
      .ingredients-card__detail-label,
      .ingredients-card__method-value,
      .ingredients-card__method-label,
      .ingredients-card__measure-title {
        color: var(--text-color-slate-gray);
      }
    }
  }

  &__item-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
  }

  &__name-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
  }

  &__removed-label-wrap {
    width: 100%;
  }

  &__removed-label {
    font-family: var(--font-family-primary);
    background-color: var(--color-danger-opacity);
    padding: var(--spacing-xxs) var(--spacing-xsm);
    border-radius: var(--border-radius-xs);
    color: var(--text-color-danger);
    font-size: var(--font-size-xxs);
    border: 1px solid var(--color-danger);
    box-shadow: var(--box-shadow-xs);
  }

  &__item-name {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-black);
  }

  &__item-cost {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-danger);
  }

  &__item-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
  }

  &__detail {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);

    @media (min-width: 768px) {
      flex-direction: row;
      align-items: center;
    }
  }

  &__detail-label {
    font-family: var(--font-family-primary);
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-sm);
  }

  &__detail-value {
    font-family: var(--font-family-primary);
    color: var(--text-color-black);
    font-weight: var(--font-weight-medium);
    margin-left: var(--spacing-xs);
  }

  &__item-methods {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
  }

  &__method {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  &__method-label {
    font-family: var(--font-family-primary);
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-xs);
    min-width: 3rem;
  }

  &__method-value {
    font-family: var(--font-family-primary);
    color: var(--text-color-black);
    flex: 1;
  }

  .old-ingredient-quantity {
    font-size: var(--font-size-sm);
    margin-left: var(--spacing-xs);
  }
}
