.haccp-card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  border: var(--border-width-xs) solid var(--color-light-gray);
  box-shadow: var(--box-shadow-xs);

  &__header {
    padding: var(--spacing-lg) var(--spacing-xxl);
    border-bottom: var(--border-width-xs) solid var(--color-light-gray);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__highlight-badge {
    background-color: var(--color-warning-opacity);
    color: var(--color-warning);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-left: auto;
  }

  &__highlight-description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--color-warning);
    margin-top: var(--spacing-xs);
    font-style: italic;
  }

  &__content {
    padding: var(--spacing-lg);
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__item {
    border: var(--border-width-xs) solid var(--color-light-gray);
    border-left: var(--border-width-lg) solid var(--color-primary);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    position: relative;
  }

  &__haccp-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    // margin-bottom: var(--spacing-md);
  }

  &__haccp-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  &__haccp-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-black);
  }
  &__haccp-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }
}
