'use client';
import React, { useState, useEffect, useContext } from 'react';
import { Box, Typography } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import NoDataView from '@/components/UI/NoDataView';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import ContentLoader from '@/components/UI/ContentLoader';
import AuthContext from '@/helper/authcontext';
import { changeRequestService } from '@/services/changeRequestService';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import { staticOptions } from '@/helper/common/staticOptions';
import Icon from '@/components/UI/AppIcon/AppIcon';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import CRView from '@/components/ChangeRequest/CRView';
import CRRemark from '@/components/ChangeRequest/CRRemark';
import dayjs from 'dayjs';
import '../reports.scss';
import '../../../app/(auth)/(users)/(change-req)/change-request/changereq.scss';

const filterFields = [
  {
    type: 'search',
    label: 'Search',
    name: 'search',
    placeholder: 'Search',
  },
  {
    type: 'select',
    label: 'Status',
    name: 'status',
    options: staticOptions?.CHANGE_REQUEST_OPTION,
    placeholder: 'Select Status',
  },
  {
    type: 'select',
    label: 'Time Period',
    name: 'timePeriod',
    options: [
      { label: 'Week', value: 'week' },
      { label: 'Month', value: 'month' },
      { label: 'Custom', value: 'custom' },
    ],
    placeholder: 'Select Time Period',
  },
  {
    type: 'date-range',
    label: 'Date Range',
    name: 'dateRange',
    placeholder: 'Select date range',
    format: 'MMM dd, yyyy',
    conditional: {
      dependsOn: 'timePeriod',
      showWhen: 'custom',
    },
  },
];

export default function ChangeRequestReports() {
  const { authState, setUserdata } = useContext(AuthContext);

  // Menu items for action dropdown
  const getActionMenuItems = (row) => {
    const menuItems = [
      {
        label: 'View',
        icon: <Icon name="Eye" size={16} />,
        onClick: (_, row) => {
          setViewingChangeRequest(row?.id);
        },
      },
    ];

    // Add Remark option conditionally
    if (
      authState?.UserPermission?.change_request === 2 &&
      row?.change_request_status !== 'rejected' &&
      row?.change_request_status !== 'closed' &&
      row?.change_request_status !== 'cancelled' &&
      row?.change_request_status !== 'deleted' &&
      row?.change_request_status !== 'approved' &&
      authState?.id !== row?.change_request_user?.id
    ) {
      menuItems.push({
        label: 'Remark',
        icon: <Icon name="MessageSquare" size={16} />,
        onClick: (_, row) => {
          setRemarkingChangeRequest(row?.id);
        },
      });
    }

    // Add Delete option conditionally
    if (
      authState?.UserPermission?.change_request === 2 &&
      authState?.id !== row?.change_request_user?.id &&
      row?.change_request_status !== 'deleted'
    ) {
      menuItems.push({
        label: 'Delete',
        icon: <Icon name="Trash2" size={16} />,
        variant: 'danger',
        onClick: (_, row) => {
          handleOpenDeleteDialog(row?.id);
        },
      });
    }

    return menuItems;
  };
  const [changeRequestList, setChangeRequestList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [filters, setFilters] = useState({});
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [viewingChangeRequest, setViewingChangeRequest] = useState(null);

  const [remarkingChangeRequest, setRemarkingChangeRequest] = useState(null);

  // CommonTable columns (adapted from ChangeRequestStaffPage)
  const columns = [
    {
      header: 'ID',
      accessor: 'id',
      sortable: false,
    },
    {
      header: 'Name',
      accessor: 'user_full_name',
      sortable: false,
      renderCell: (_, row) => (
        <CommonUserDetails
          userData={row?.change_request_user}
          searchValue={searchValue}
          page={page}
          rowsPerPage={rowsPerPage}
          setUserdata={setUserdata}
          authState={authState}
          navigationProps={{ changeReq: true }}
        />
      ),
    },
    {
      header: 'Subject',
      accessor: 'change_request_subject',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    {
      header: 'Date',
      accessor: 'createdAt',
      sortable: false,
      renderCell: (value) => DateFormat(value, 'dates'),
    },
    {
      header: 'Status',
      accessor: 'change_request_status',
      sortable: false,
      renderCell: (value) => (
        <Box className="d-flex align-center justify-start h100 text-capital">
          {value === 'reopened' ? (
            <Typography className="sub-title-text status-yellow fw600">
              Re-opened
            </Typography>
          ) : value === 'pending' ? (
            <Typography className="sub-title-text draft fw600">
              {value}
            </Typography>
          ) : value === 'approved' ? (
            <Typography className="sub-title-text active-onboarding fw600">
              {value}
            </Typography>
          ) : value === 'closed' ? (
            <Typography className="sub-title-text closed fw600">
              {value}
            </Typography>
          ) : value === 'rejected' ? (
            <Typography className="sub-title-text rejected fw600">
              {value}
            </Typography>
          ) : (
            <Typography className="sub-title-text fw600">{value}</Typography>
          )}
        </Box>
      ),
    },
    // Actions column is now handled by CommonTable's actionMenuItems prop
  ];

  // Delete handlers
  const deleteChangeRequestHandler = async (id) => {
    try {
      const data = await changeRequestService.deleteChangeRequest(id);
      if (data?.status) {
        handleCloseDeleteDialog();
        if (changeRequestList?.length === 1 && page !== 1) {
          const searchTerm = filters.search || '';
          const statusValue = filters.status || '';
          let dateValue = '';
          let startDate = '';
          let endDate = '';

          if (filters.timePeriod === 'custom' && filters.dateRange) {
            startDate = filters.dateRange?.[0]
              ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
              : '';
            endDate = filters.dateRange?.[1]
              ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
              : '';
          } else if (filters.timePeriod && filters.timePeriod !== 'custom') {
            const dateRange = getDateRangeForPeriod(filters.timePeriod);
            startDate = dateRange.startDate;
            endDate = dateRange.endDate;
            dateValue = getDateForPeriod(filters.timePeriod);
          }

          setPage(page - 1);
          getChangeRequestDetails(
            searchTerm,
            page - 1,
            rowsPerPage,
            statusValue,
            dateValue,
            startDate,
            endDate
          );
        } else {
          const searchTerm = filters.search || '';
          const statusValue = filters.status || '';
          let dateValue = '';
          let startDate = '';
          let endDate = '';

          if (filters.timePeriod === 'custom' && filters.dateRange) {
            startDate = filters.dateRange?.[0]
              ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
              : '';
            endDate = filters.dateRange?.[1]
              ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
              : '';
          } else if (filters.timePeriod && filters.timePeriod !== 'custom') {
            const dateRange = getDateRangeForPeriod(filters.timePeriod);
            startDate = dateRange.startDate;
            endDate = dateRange.endDate;
            dateValue = getDateForPeriod(filters.timePeriod);
          }

          getChangeRequestDetails(
            searchTerm,
            page,
            rowsPerPage,
            statusValue,
            dateValue,
            startDate,
            endDate
          );
        }
        setApiMessage('success', data?.message);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  // Calculate date range based on time period
  const getDateRangeForPeriod = (timePeriod) => {
    const now = dayjs();
    switch (timePeriod) {
      case 'week':
        return {
          startDate: now.startOf('week').format('YYYY-MM-DD'),
          endDate: now.endOf('week').format('YYYY-MM-DD'),
        };
      case 'month':
        return {
          startDate: now.startOf('month').format('YYYY-MM-DD'),
          endDate: now.endOf('month').format('YYYY-MM-DD'),
        };
      default:
        return { startDate: '', endDate: '' };
    }
  };

  // Calculate date based on time period (backward compatibility)
  const getDateForPeriod = (timePeriod) => {
    const now = dayjs();
    switch (timePeriod) {
      case 'week':
        return now.startOf('week').format('YYYY-MM-DD');
      case 'month':
        return now.startOf('month').format('YYYY-MM-DD');
      default:
        return '';
    }
  };

  // Get change request details from API
  const getChangeRequestDetails = async (
    search = '',
    pageNo = 1,
    Rpp = rowsPerPage,
    statusValue = '',
    dateValue = '',
    startDate = '',
    endDate = ''
  ) => {
    setLoader(true);
    try {
      // Use the enhanced API with date range support
      const data = await changeRequestService.getChangeRequestList(
        search,
        pageNo,
        statusValue,
        dateValue,
        Rpp,
        startDate,
        endDate
      );

      if (data) {
        setPage(data?.page || pageNo);
        setTotalCount(data?.count || 0);
        setChangeRequestList(data?.data || []);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setChangeRequestList([]);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Handle filter apply
  const handleApplyFilters = (values) => {
    setFilters(values);
    setSearchValue(values.search || '');
    const searchTerm = values.search || '';
    const statusValue = values.status || '';

    let dateValue = '';
    let startDate = '';
    let endDate = '';

    // Handle time period and date range
    if (values.timePeriod === 'custom' && values.dateRange) {
      // For custom date range, use start and end dates
      startDate = values.dateRange?.[0]
        ? new Date(values.dateRange[0]).toISOString().split('T')[0]
        : '';
      endDate = values.dateRange?.[1]
        ? new Date(values.dateRange[1]).toISOString().split('T')[0]
        : '';
    } else if (values.timePeriod && values.timePeriod !== 'custom') {
      // For week/month, use the calculated date range
      const dateRange = getDateRangeForPeriod(values.timePeriod);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
      // Keep backward compatibility with single date
      dateValue = getDateForPeriod(values.timePeriod);
    }

    setPage(1);
    getChangeRequestDetails(
      searchTerm,
      1,
      rowsPerPage,
      statusValue,
      dateValue,
      startDate,
      endDate
    );
  };

  // Handle pagination
  const handlePageChange = (newPage) => {
    setPage(newPage);
    const searchTerm = filters.search || '';
    const statusValue = filters.status || '';

    let dateValue = '';
    let startDate = '';
    let endDate = '';

    if (filters.timePeriod === 'custom' && filters.dateRange) {
      startDate = filters.dateRange?.[0]
        ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
        : '';
      endDate = filters.dateRange?.[1]
        ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
        : '';
    } else if (filters.timePeriod && filters.timePeriod !== 'custom') {
      const dateRange = getDateRangeForPeriod(filters.timePeriod);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
      dateValue = getDateForPeriod(filters.timePeriod);
    }

    getChangeRequestDetails(
      searchTerm,
      newPage,
      rowsPerPage,
      statusValue,
      dateValue,
      startDate,
      endDate
    );
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    const searchTerm = filters.search || '';
    const statusValue = filters.status || '';

    let dateValue = '';
    let startDate = '';
    let endDate = '';

    if (filters.timePeriod === 'custom' && filters.dateRange) {
      startDate = filters.dateRange?.[0]
        ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
        : '';
      endDate = filters.dateRange?.[1]
        ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
        : '';
    } else if (filters.timePeriod && filters.timePeriod !== 'custom') {
      const dateRange = getDateRangeForPeriod(filters.timePeriod);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
      dateValue = getDateForPeriod(filters.timePeriod);
    }

    getChangeRequestDetails(
      searchTerm,
      1,
      newRowsPerPage,
      statusValue,
      dateValue,
      startDate,
      endDate
    );
  };

  // Initial load
  useEffect(() => {
    getChangeRequestDetails();
  }, []);

  return (
    <>
      <Box className="report-main-container">
        {viewingChangeRequest ? (
          <Box className="cr-view-container">
            <Box className="cr-details-section">
              <CRView
                crId={viewingChangeRequest}
                onBack={() => setViewingChangeRequest(null)}
              />
            </Box>
          </Box>
        ) : remarkingChangeRequest ? (
          <Box className="cr-view-container">
            <Box className="cr-details-section">
              <CRRemark
                crId={remarkingChangeRequest}
                onBack={() => setRemarkingChangeRequest(null)}
                onSuccess={() => {
                  setRemarkingChangeRequest(null);
                  // Refresh the data
                  getChangeRequestDetails();
                }}
              />
            </Box>
          </Box>
        ) : (
          <>
            <FilterCollapse
              fields={filterFields}
              onApply={handleApplyFilters}
              buttonText="Apply Filters"
              initialValues={filters}
            />

            <Box className="report-table-container">
              {loader ? (
                <ContentLoader />
              ) : changeRequestList && changeRequestList?.length === 0 ? (
                <NoDataView
                  title="No Change Request Records Found"
                  description="There is no Change Request data available at the moment."
                />
              ) : (
                <CommonTable
                  columns={columns}
                  data={changeRequestList}
                  pageSize={rowsPerPage}
                  currentPage={page}
                  totalCount={totalCount}
                  onPageChange={handlePageChange}
                  onRowsPerPageChange={handleRowsPerPageChange}
                  actionMenuItems={getActionMenuItems}
                />
              )}
            </Box>
          </>
        )}
      </Box>

      <DialogBox
        open={deleteDialogOpen}
        onClose={handleCloseDeleteDialog}
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={() => deleteChangeRequestHandler(deleteId)}
            text="Are you sure you want to delete this Change Request?"
          />
        }
      />
    </>
  );
}
